import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { useControls } from 'leva';
import styles from './Scene1Overlay.module.css';

function Scene1Overlay({ style, scrollProgress = 0, isVisible = true }) {
  const overlayRef = useRef(null);
  const centerContentRef = useRef(null);
  const previousScrollProgress = useRef(0);
  const hasScrolledBeyondThreshold = useRef(false);

  // Leva controls for testing animation values
  const {
    animationThreshold,
    maxBlur,
    enableAnimation,
    testProgress,
    centerAppearThreshold,
    centerBlurAmount,
    showCenterContent,
    debugCenterOpacity
  } = useControls('Scene1 Overlay Animation', {
    enableAnimation: true,
    animationThreshold: { value: 0.25, min: 0.1, max: 1, step: 0.01 },
    maxBlur: { value: 10, min: 0, max: 20, step: 1 },
    testProgress: { value: 0, min: 0, max: 1, step: 0.01 },
    centerAppearThreshold: { value: 0.55, min: 0.3, max: 0.8, step: 0.01 },
    centerBlurAmount: { value: 8, min: 0, max: 15, step: 1 },
    showCenterContent: false,
    debugCenterOpacity: { value: 1, min: 0, max: 1, step: 0.1 }
  });

  // Control overall overlay visibility based on isVisible prop
  useEffect(() => {
    if (overlayRef.current) {
      gsap.set(overlayRef.current, {
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'none' : 'none' // Always none for mouse passthrough
      });
    }
  }, [isVisible]);

  // Initial setup - CRITICAL: Set initial states immediately to prevent flash
  useEffect(() => {

    // Hide center content initially
    if (centerContentRef.current) {
      gsap.set(centerContentRef.current, {
        opacity: 0,
        filter: 'blur(10px)'
      });
    }

    // Set initial state for hero elements based on current scroll
    if (overlayRef.current && centerContentRef.current) {
      const heroElements = Array.from(overlayRef.current.children).filter(
        child => child !== centerContentRef.current
      );

      // If we're starting at 0 scroll, show hero content
      // If we're starting at higher scroll (returning from transition), hide it
      const initialOpacity = scrollProgress === 0 ? 1 : 0;

      heroElements.forEach(element => {
        gsap.set(element, {
          opacity: initialOpacity,
          filter: 'blur(0px)'
        });
      });
    }

    // Reset scroll tracking when component mounts
    hasScrolledBeyondThreshold.current = false;
    previousScrollProgress.current = 0;
  }, []); // Empty dependency array - only run on mount

  // Separate animations for hero content and center content
  useEffect(() => {
    if (!overlayRef.current || !centerContentRef.current || !enableAnimation || !isVisible) return;

    // Use test progress if available, otherwise use scroll progress
    const currentProgress = testProgress > 0 ? testProgress : scrollProgress;

    // HERO CONTENT ANIMATION (fade out early) - Target specific elements, NOT center content
    const heroAnimationProgress = Math.min(currentProgress / animationThreshold, 1);

    // Get all children except the center content
    const heroElements = Array.from(overlayRef.current.children).filter(
      child => child !== centerContentRef.current
    );

    // Track if we've scrolled beyond the animation threshold
    if (currentProgress > animationThreshold) {
      hasScrolledBeyondThreshold.current = true;
    }

    // Calculate hero opacity - prevent flashing when returning to scene
    let heroOpacity = 1 - heroAnimationProgress;

    // If we're returning to 0% scroll but have previously scrolled beyond threshold,
    // and we're not in test mode, keep hero content hidden to prevent flash
    if (currentProgress === 0 && hasScrolledBeyondThreshold.current && testProgress === 0) {
      heroOpacity = 0;
    }

    // Update previous scroll progress
    previousScrollProgress.current = currentProgress;

    // Apply hero content fade out to specific elements only
    heroElements.forEach(element => {
      gsap.to(element, {
        duration: 0.1,
        ease: "none",
        filter: `blur(${heroAnimationProgress * maxBlur}px)`,
        opacity: heroOpacity,
        overwrite: true
      });
    });

    // CENTER CONTENT ANIMATION (appears later)
    let centerOpacity = 0;
    let centerBlur = 10;

    // Debug override
    if (showCenterContent) {
      centerOpacity = 1;
      centerBlur = 0;
    } else {
      // Normal scroll-based animation
      if (currentProgress >= 0.40 && currentProgress < 0.50) {
        // Fade in from 40% to 50%
        const progress = (currentProgress - 0.40) / 0.10; // 10% range
        centerOpacity = progress;
        centerBlur = 10 * (1 - progress);
      } else if (currentProgress >= 0.50 && currentProgress <= 0.90) {
        // Fully visible from 50% to 90%
        centerOpacity = 1;
        centerBlur = 0;
      } else if (currentProgress > 0.90) {
        // Fade out after 90%
        const progress = Math.min((currentProgress - 0.90) / 0.10, 1); // 10% range
        centerOpacity = 1 - progress;
        centerBlur = progress * 10;
      }
    }

    // Apply center content animation with immediate effect
    gsap.set(centerContentRef.current, {
      opacity: centerOpacity,
      filter: `blur(${centerBlur}px)`,
      pointerEvents: 'none' // Always none - individual elements will have auto
    });

    // Debug log
    console.log(`Scroll: ${(currentProgress * 100).toFixed(1)}%, Center Opacity: ${centerOpacity.toFixed(2)}, Center Blur: ${centerBlur.toFixed(1)}px`);

  }, [scrollProgress, animationThreshold, maxBlur, enableAnimation, testProgress, showCenterContent, isVisible]);

  return (
    <div
      ref={overlayRef}
      className={styles.overlay}
      style={{ pointerEvents: 'none', opacity: 1, ...style }}
    >
      <div className={styles.topNavBar}>
        <img src="/blcks_logo_icon.svg" alt="BLCKS Logo" className={styles.logoImg} />
        <img src="/menu_icon.svg" alt="Menu Icon" className={styles.menuIconTopRight} />
      </div>
      <div className={styles.heroContentWrapper}>
        <h1 className={styles.heroHeading}>
          Wir entwickeln leistungsstarke KI-Systeme für skalierbares Wachstum
        </h1>
      </div>
      {/* 6 absolutely positioned hero texts inside overlay */}
      <div className={styles.heroText1}>BLCK.LABS V01<br />GRAZ. AUT<br />NUM ID : A420</div>
      <div className={styles.heroText2}>AI.RESEARCH<br />LAB 10</div>
      <div className={styles.heroText3}>PROFIL #01</div>
      <div className={styles.heroText4}>BLCKS V01<br />REFERENCE ID 2308106</div>
      <div className={styles.heroText5}>AI DEVELOPMENT<br />STUDIO COMPANY</div>
      <div className={styles.heroText6}>WEB RENDERED v0.2.4</div>
      <div className={styles.heroText7}>KEY GENERATION v0.1.2</div>
      <div className={styles.subContentWrapper}>
        <div className={styles.subText}>
          Wir entwickeln, optimieren und skalieren intelligente Automatisierungslösungen, die Prozesse verschlanken, Effizienz steigern und den ROI maximieren.
        </div>
        <button className={styles.subButton}>ENTDECKE BLCKS</button>
      </div>
      <div className={styles.scrollIndicator}>
        <p className={styles.scrollText}>SCROLL DOWN</p>
        <img src="/scrolldownpfeil_grey.svg" alt="Scroll Down" className={styles.scrollArrow} />
      </div>

      {/* Center Content - Appears at center position - NOW INSIDE OVERLAY */}
      <div ref={centerContentRef} className={styles.centerContent}>
        {/* Combined centerHeading1 and centerButton - Center Center */}
        <div className={styles.centerMainWrapper}>
          <h2 className={styles.centerHeading1}>
            Wir revolutionieren die Art und Weise wie smarte Unternehmen arbeiten.
          </h2>
          <button className={styles.centerButton}>INSIDE BLCKS</button>
        </div>

        {/*<h2 className={styles.centerHeading2}>
          die Art und Weise
        </h2>
        <h2 className={styles.centerHeading3}>
          wie smarte Unternehmen arbeiten.
        </h2>*/}

        {/* Center Content Wrapper (without border) */}
        <div className={styles.centerContentWrapper}>
          <p className={styles.centerSubtext}>
            Wir glauben, dass moderne Unternehmen nicht komplizierter, sondern schlauer arbeiten sollten – mit smarten Tools, klaren Prozessen und richtig guten Ideen. Wir kombinieren technisches Know-how mit strategischem Denken und haben dabei immer ein Ziel vor Augen: moderne Arbeit einfacher, schneller und smarter zu machen.
          </p>
        </div>

        {/* 3 Small Texts positioned around the scene */}
        <div className={styles.centerText1}>WIR BAUEN AUTOMATIONEN, <br />DIE MITDENKEN - NICHT NUR ARBEITEN</div>
        <div className={styles.centerText2}>ROUTINE WIRD ERSETZT. ZEIT<br />WIRD FREIGESETZT. FOKUS ENTSTEHT.</div>
        <div className={styles.centerText3}>KI, DIE SICH ANFÜHLT WIE EIN SMARTER KOLLEGE<br />- VERLÄSSLICH UND SCHNELL.</div>
      </div>
    </div>
  );
}

export default Scene1Overlay;
