import { Suspense, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { useControls } from 'leva';
import './AppAI.css';

// Import modular components
import SceneManager from './components/SceneManager/SceneManager';
import Scene1 from './components/SceneManager/Scene1';
import Scene2 from './components/SceneManager/Scene2';
import CameraController from './components/SceneManager/CameraController';
import Scene1Overlay from './components/SceneManager/Scene1Overlay';

// Alternative AppAI Component with separate scroll areas
export default function AppAI() {
  const [currentScene, setCurrentScene] = useState(1);
  
  // Leva controls for transition
  const {
    transitionProgress,
    sceneSelector
  } = useControls('Scene Transition', {
    transitionProgress: { value: 0, min: 0, max: 1, step: 0.01 },
    sceneSelector: { 
      value: 1, 
      options: { 'Scene 1': 1, 'Scene 2': 2 },
      onChange: (value) => setCurrentScene(value)
    }
  });

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      {/* Scene 1 - Scrollable area */}
      {currentScene === 1 && (
        <div 
          style={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100vw', 
            height: '100vh', 
            overflow: 'hidden',
            zIndex: 1
          }}
        >
          <Canvas
            camera={{ position: [0, 0.35, 2], fov: 45 }}
            gl={{ alpha: true, antialias: true, powerPreference: 'high-performance' }}
            style={{ width: '100%', height: '100%' }}
          >
            <Suspense fallback={null}>
              <CameraController />
              <Scene1 />
            </Suspense>
          </Canvas>

          {/* Scene1 Overlay - HTML/CSS Content */}
          <Scene1Overlay scrollProgress={0} />
        </div>
      )}

      {/* Scene 2 - Separate scrollable area */}
      {currentScene === 2 && (
        <div 
          style={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100vw', 
            height: '100vh', 
            overflow: 'auto',
            zIndex: 1
          }}
        >
          <Canvas
            camera={{ position: [0, 3, 0], fov: 45 }}
            gl={{ alpha: true, antialias: true, powerPreference: 'high-performance' }}
            style={{ width: '100%', height: '100%' }}
          >
            <Suspense fallback={null}>
              <CameraController />
              <Scene2 />
            </Suspense>
          </Canvas>
          
          {/* Scroll content for Scene 2 */}
          <div style={{
            position: 'absolute',
            top: '100vh',
            left: 0,
            width: '100%',
            height: '400vh',
            pointerEvents: 'none',
            background: 'transparent'
          }} />
        </div>
      )}

      {/* Transition mode - uses SceneManager */}
      {transitionProgress > 0 && transitionProgress < 1 && (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          width: '100vw', 
          height: '100vh',
          zIndex: 2
        }}>
          <Canvas
            camera={{ position: [0, 0.35, 2], fov: 45 }}
            gl={{ alpha: true, antialias: true, powerPreference: 'high-performance' }}
            style={{ width: '100%', height: '100%' }}
          >
            <Suspense fallback={null}>
              <CameraController />
              <SceneManager
                Scene1Component={Scene1}
                Scene2Component={Scene2}
                transition={transitionProgress}
              />
            </Suspense>
          </Canvas>
        </div>
      )}
    </div>
  );
}
