precision highp float;

in vec2 vUv;
in vec3 wPos;
in vec2 vScreenUV;

uniform vec3 cameraPosition;
uniform float time;
uniform float uNear;
uniform float uFar;
uniform float fov;
uniform vec2 resolution;
uniform vec4 cameraQuaternion;

// --- <PERSON><PERSON> inlined structs.glsl ---
struct <PERSON><PERSON><PERSON><PERSON> {
  bool hit;
  vec3 position;
  float distance;
};

struct RaymarchResult {
  vec4 color;
  float depth;
};
// --- End inlined structs.glsl ---

// --- <PERSON>gin inlined rayMarch and dependencies ---

// --- <PERSON>gin inlined getSceneHit and dependencies ---
const float PI = 3.14159265359;

// --- Begin inlined snoise2 (2D simplex noise) ---
vec3 mod289(vec3 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec2 mod289(vec2 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec3 permute(vec3 x) {
  return mod289(((x*34.0)+1.0)*x);
}
float snoise2(vec2 v)
{
  const vec4 C = vec4(0.211324865405187,  // (3.0-sqrt(3.0))/6.0
                      0.366025403784439,  // 0.5*(sqrt(3.0)-1.0)
                     -0.577350269189626,  // -1.0 + 2.0 * C.x
                      0.024390243902439); // 1.0 / 41.0
  vec2 i  = floor(v + dot(v, C.yy) );
  vec2 x0 = v -   i + dot(i, C.xx);
  vec2 i1;
  i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
  vec4 x12 = x0.xyxy + C.xxzz;
  x12.xy -= i1;
  i = mod289(i);
  vec3 p = permute( permute( i.y + vec3(0.0, i1.y, 1.0 ))
    + i.x + vec3(0.0, i1.x, 1.0 ));
  vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
  m = m*m ;
  m = m*m ;
  vec3 x = 2.0 * fract(p * C.www) - 1.0;
  vec3 h = abs(x) - 0.5;
  vec3 ox = floor(x + 0.5);
  vec3 a0 = x - ox;
  m *= 1.79284291400159 - 0.85373472095314 * ( a0*a0 + h*h );
  vec3 g;
  g.x  = a0.x  * x0.x  + h.x  * x0.y;
  g.yz = a0.yz * x12.xz + h.yz * x12.yw;
  return 130.0 * dot(m, g);
}
// --- End inlined snoise2 ---

// --- Begin fixed snoise3 (3D simplex noise, Ashima Arts) ---
vec4 mod289(vec4 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}
vec4 permute(vec4 x) {
  return mod289(((x*34.0)+1.0)*x);
}
vec4 taylorInvSqrt(vec4 r) {
  return 1.79284291400159 - 0.85373472095314 * r;
}
float snoise3(vec3 v) {
  const vec2  C = vec2(1.0/6.0, 1.0/3.0) ;
  const vec4  D = vec4(0.0, 0.5, 1.0, 2.0);
  vec3 i  = floor(v + dot(v, C.yyy) );
  vec3 x0 =   v - i + dot(i, C.xxx) ;
  vec3 g = step(x0.yzx, x0.xyz);
  vec3 l = 1.0 - g;
  vec3 i1 = min( g.xyz, l.zxy );
  vec3 i2 = max( g.xyz, l.zxy );
  vec3 x1 = x0 - i1 + C.xxx;
  vec3 x2 = x0 - i2 + C.yyy;
  vec3 x3 = x0 - D.yyy;
  i = mod289(i);
  vec4 p = permute(
    permute(
      permute(
        i.z + vec4(0.0, i1.z, i2.z, 1.0))
      + i.y + vec4(0.0, i1.y, i2.y, 1.0))
    + i.x + vec4(0.0, i1.x, i2.x, 1.0)
  );
  float n_ = 0.142857142857; // 1.0/7.0
  vec3  ns = n_ * D.wyz - D.xzx;
  vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
  vec4 x_ = floor(j * ns.z);
  vec4 y_ = floor(j - 7.0 * x_ );
  vec4 x = x_ *ns.x + ns.yyyy;
  vec4 y = y_ *ns.x + ns.yyyy;
  vec4 h = 1.0 - abs(x) - abs(y);
  vec4 b0 = vec4( x.xy, y.xy );
  vec4 b1 = vec4( x.zw, y.zw );
  vec4 s0 = floor(b0)*2.0 + 1.0;
  vec4 s1 = floor(b1)*2.0 + 1.0;
  vec4 sh = -step(h, vec4(0.0));
  vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy ;
  vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww ;
  vec3 p0 = vec3(a0.xy,h.x);
  vec3 p1 = vec3(a0.zw,h.y);
  vec3 p2 = vec3(a1.xy,h.z);
  vec3 p3 = vec3(a1.zw,h.w);
  vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
  p0 *= norm.x;
  p1 *= norm.y;
  p2 *= norm.z;
  p3 *= norm.w;
  vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
  m = m * m;
  return 42.0 * dot( m*m, vec4( dot(p0,x0), dot(p1,x1),
                                dot(p2,x2), dot(p3,x3) ) );
}
// --- End fixed snoise3 ---

// --- Begin inlined valueRemap ---
float valueRemap(
  float value,
  float min,
  float max,
  float newMin,
  float newMax
) {
  return newMin + (value - min) * (newMax - newMin) / (max - min);
}
// --- End inlined valueRemap ---

uniform vec3 uHitPosition;
uniform float noiseScale;
uniform float noiseLength;
uniform sampler2D uFlowTexture;
uniform float pyramidReveal;
uniform sampler2D uNoiseTexture;
uniform float mouseSpeed;
uniform mat4 uPyramidMatrix;
uniform float uFlowSize;
// AVAILABLE: uniform float time;

float sdSphere(vec3 position, float radius) {
  return length(position) - radius;
}

float sdPlane(vec3 position) {
  return position.y;
}

float tetrahedron(vec3 p, float size) {
  p = (uPyramidMatrix * vec4(p, 1.0)).xyz;
  p /= size;
  float d = (max(abs(p.x + p.y) - p.z, abs(p.x - p.y) + p.z) - 1.0) / sqrt(3.0);
  return d * size;
}

float plane(vec3 p, vec3 c, vec3 n) {
  return dot(p - c, n);
}

// sdf functions

float opUnion(float d1, float d2) {
  return min(d1, d2);
}

float opIntersection(float d1, float d2) {
  return max(d1, d2);
}

float opSmoothUnion(float d1, float d2, float k) {
  float h = clamp(0.5 + 0.5 * (d2 - d1) / k, 0.0, 1.0);
  return mix(d2, d1, h) - k * h * (1.0 - h);
}

// remap functions

float gain(float x, float k) {
  float a = 0.5 * pow(2.0 * (x < 0.5 ? x : 1.0 - x), k);
  return x < 0.5
    ? a
    : 1.0 - a;
}

float expStep(float x, float n) {
  return exp2(-exp2(n) * pow(x, n));
}

// a when t = 0
// b when t = 0.5
// c when t = 1
float mix3(float a, float b, float c, float t) {
  if (t < 0.5) {
    return mix(a, b, t * 2.0);
  } else {
    return mix(b, c, (t - 0.5) * 2.0);
  }
}

float almostUnitIdentity(float x) {
  return x * x * (2.0 - x);
}

// blur

vec4 blurTexture(sampler2D sam, vec2 uv) {
  vec2 e = vec2(1.0) / vec2(textureSize(sam, 0));
  vec4 sum = vec4(0.0);
  float weight = 0.0;
  // Gaussian kernel weights
  float kernel[9] = float[](
    0.077847,
    0.123317,
    0.077847,
    0.123317,
    0.195346,
    0.123317,
    0.077847,
    0.123317,
    0.077847
  );

  // 3x3 kernel
  for (int i = -1; i <= 1; i++) {
    for (int j = -1; j <= 1; j++) {
      vec2 offset = vec2(float(i), float(j)) * e;
      float w = kernel[(i + 1) * 3 + (j + 1)];
      sum += texture(sam, uv + offset) * w;
      weight += w;
    }
  }

  return sum / weight;
}

// noise functions
vec3 getNoise(vec2 uv) {
  vec3 noise = texture(uNoiseTexture, uv).xyz;
  return noise;
}

float getCircleSin(vec3 p) {
  float d = distance(p, uHitPosition * 0.5);
  float s = sin(d * 30.0);
  return s * 0.5 + 0.5;
}

// objects
float getOrbeHit(vec3 p) {
  vec3 orbeP = p;
  float pyramidMinP = -0.8;
  float pyramidMaxP = 0.5;
  float orbeYPos = mix(pyramidMinP, pyramidMaxP, pyramidReveal);

  float noiseAmmount = 1.0 - pyramidReveal;

  if (noiseAmmount > 0.0) {
    float noise = snoise3(
      (orbeP.xyz + vec3(0.0, time * 0.1 - orbeYPos * 0.9, time * 0.2)) * 5.0
    );

    float noiseMult = 1.0 - expStep(noiseAmmount, 2.0);

    // hard noise
    float noise1 = noise;
    // noise1 = pow(noise1, 0.5);
    noise1 *= noiseMult;

    // orbeP.x += noise1 * 0.01;
    orbeP.z += noise1 * 0.06;
    orbeP.y += noise * 0.1 * noiseMult;
  }

  orbeP -= vec3(0.0, orbeYPos, 0.0);
  float scale = 0.2;
  return tetrahedron(orbeP, 0.2);
}

float getSpikesHit(vec3 pos, float flow) {
  vec3 p = pos;

  float shiftInlfuence = p.y * 10.0;

  p -= uHitPosition;
  float dist = length(p) * 2.0;
  vec3 direction = normalize(p);

  float dist2 = dist - 0.01;
  dist2 = max(dist2, 0.0);
  p.xz -= direction.xz * shiftInlfuence * dist2 * 0.1;

  p += uHitPosition;

  p += uHitPosition * 0.2;
  float sinCos = sin(p.x * 60.0) * cos(p.z * 60.0);
  return sinCos;
}

float flowEdge = 0.4;

float getFlowHit(vec3 p) {
  vec2 uv = p.xz;
  uv = vec2(
    valueRemap(uv.x, -uFlowSize, uFlowSize, 0.0, 1.0),
    valueRemap(uv.y, -uFlowSize, uFlowSize, 0.0, 1.0)
  );
  uv = clamp(uv, 0.0, 1.0);
  uv.y = 1.0 - uv.y;
  float flow = texture(uFlowTexture, uv).x;
  // flow = smoothstep(0.0, 1.0, flow);
  // remap from 0-1 to -1-1
  flow *= 2.0;
  flow -= 1.0;

  // flow = almostUnitIdentity(flow);
  // flow = -0.2;

  flow *= 0.2;

  // smoot out to edges
  float edge = smoothstep(0.0, flowEdge, uv.x);
  edge *= smoothstep(0.0, flowEdge, uv.y);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.x);
  edge *= smoothstep(1.0, 1.0 - flowEdge, uv.y);
  // edge = 1.0 - edge;

  flow *= edge;

  return flow;
}

// Add ripple uniforms - increased to 15 for trail effects
uniform vec2 ripplePositions[15];
uniform float rippleTimes[15];
uniform float rippleIntensities[15];

float getFloorHit(vec3 p) {
  // plane with flow
  float flow = getFlowHit(p);
  float planeY = 0.0;
  planeY += flow;

  // --- POWERFUL Ripple effect ---
  for (int i = 0; i < 15; i++) {
    float t = rippleTimes[i];
    if (t > 0.0 && t < 3.0) {
      float dist = length(p.xz - ripplePositions[i]);
      float intensity = rippleIntensities[i];

      // Much more powerful ripple calculation
      // Base amplitude increased from 0.08 to 0.5+ based on intensity
      float baseAmplitude = 0.3 + (intensity * 0.4); // 0.3 to 1.1 amplitude range

      // Multiple wave frequencies for complex wave patterns
      float wave1 = sin(12.0 * dist - 8.0 * t) * exp(-2.0 * dist); // Primary wave
      float wave2 = sin(24.0 * dist - 12.0 * t) * exp(-3.0 * dist) * 0.5; // Secondary wave
      float wave3 = sin(6.0 * dist - 4.0 * t) * exp(-1.5 * dist) * 0.3; // Long wave

      // Combine waves for complex ripple pattern
      float combinedWave = wave1 + wave2 + wave3;

      // Time-based fade with longer duration
      float timeFade = (1.0 - t / 3.0);
      timeFade = timeFade * timeFade; // Quadratic fade for smoother decay

      // Distance-based spread - less aggressive falloff for wider ripples
      float distanceSpread = exp(-1.0 * dist);

      float ripple = baseAmplitude * combinedWave * timeFade * distanceSpread;
      planeY += ripple;
    }
  }

  vec3 pPlane = p - vec3(0.0, planeY, 0.0);
  float plane = sdPlane(pPlane);
  return plane;
}

float getSceneHit(vec3 p) {
  float floorHit = getFloorHit(p);
  return floorHit * 0.3;

  float noise =
    snoise3((p.xyz * 1.5 + vec3(0.0, time * 2.1, time * 0.2)) * 5.0) * 0.5 +
    0.5;

  float orbeHit = getOrbeHit(p);

  float sdf = orbeHit;

  float smoothFactor = smoothstep(0.0, 0.1, pyramidReveal);
  float inverseFactor = 1.0 - smoothstep(0.8, 1.0, pyramidReveal);

  smoothFactor *= inverseFactor;
  smoothFactor *= noise;

  if (pyramidReveal < 1.95) {
    float floorHit = getFloorHit(p);
    sdf = opSmoothUnion(sdf, floorHit, 0.3 * smoothFactor);
  }

  return sdf * 0.3;
}
// --- End inlined getSceneHit and dependencies ---

uniform samplerCube envMap;
uniform mat3 envMapRotation;
uniform sampler2D uMatcap;

const float GRADIENT_BIAS = 0.01;

// Normal calculation function (using gradient):
const vec3 GRADIENT_STEP = vec3(GRADIENT_BIAS, 0.0, 0.0);
vec3 getNormal(vec3 p) {
  float gradientX =
    getSceneHit(p + GRADIENT_STEP.xyy) - getSceneHit(p - GRADIENT_STEP.xyy);
  float gradientY =
    getSceneHit(p + GRADIENT_STEP.yxy) - getSceneHit(p - GRADIENT_STEP.yxy);
  float gradientZ =
    getSceneHit(p + GRADIENT_STEP.yyx) - getSceneHit(p - GRADIENT_STEP.yyx);
  return normalize(vec3(gradientX, gradientY, gradientZ));
}

vec3 mainColor = vec3(0.1);

vec3 lightDirection = normalize(vec3(0.0, 1.0, 1.0));

vec3 getLight(vec3 p, vec3 reflectedNormal) {
  float lambert = dot(reflectedNormal, lightDirection);
  lambert = clamp(lambert, 0.0, 1.0) * 0.9;
  return vec3(lambert);
}

vec3 getEnv3(vec3 normal) {
  vec4 envColor = texture(envMap, envMapRotation * normal);

  return envColor.rgb;
}

// vec3 sampleMatcap(vec3 normal) {
//   vec2 uv = vec2(normal.x * 0.5 + 0.5, normal.y * 0.5 + 0.5);
//   return texture(uMatcap, uv).rgb;
// }

vec3 sampleMatcap(vec3 reflected) {
  float m = 2.8284271247461903 * sqrt(reflected.z + 1.0);
  vec2 uv = reflected.xy / m + 0.5;
  vec3 mat = texture(uMatcap, uv).rgb;

  return mat;
}

vec3 getSurface(vec3 p, vec3 rayDirection) {
  vec3 viewDir = normalize(-rayDirection);
  vec3 normal = getNormal(p);

  vec3 reflectedNormal = reflect(viewDir, normal);

  vec3 env = getEnv3(reflectedNormal);

  vec3 matcap = sampleMatcap(reflectedNormal);

  // vec3 light = getLight(p, normal);

  // return reflectedNormal;

  return matcap;
}

const float SURFACE_DIST = 0.0001;
const int MAX_STEPS = 200;

RayResult castRay(
  vec3 ro,
  vec3 rd,
  float maxDistance,
  float surfaceDistance,
  int maxSteps
) {
  // depth
  float d0 = 0.0;
  float hitPoint = getSceneHit(ro);
  for (int i = 0; i < MAX_STEPS; i++) {
    vec3 p = ro + d0 * rd;
    hitPoint = getSceneHit(p);
    d0 += hitPoint;
    if (hitPoint < surfaceDistance || d0 >= maxDistance) {
      break;
    }
    ;
  }
  bool isHit = hitPoint < surfaceDistance;
  vec3 p = ro + d0 * rd;
  return RayResult(isHit, p, d0);
}

RaymarchResult rayMarch(vec3 rayPosition, vec3 rayDirection, float maxDepth) {
  vec4 result = vec4(1.0, 1.0, 1.0, 0.0);

  float distance = 0.0;

  RayResult hit = castRay(
    rayPosition,
    rayDirection,
    maxDepth,
    SURFACE_DIST,
    MAX_STEPS
  );

  if (hit.hit) {
    vec3 color = getSurface(hit.position, rayDirection);
    color = hit.distance < 0.01 ? vec3(1.0) : color;
    result = vec4(color, 1.0);
    distance = hit.distance;
  } else {
    distance = 1.0;
  }

  return RaymarchResult(result, distance);
}

// --- End inlined rayMarch and dependencies ---

// --- Begin inlined linearizeDepth ---
float linearizeDepth(float depth, float near, float far) {
  float z = depth * 2.0 - 1.0; // back to NDC
  return 2.0 * near * far / (far + near - z * (far - near));
}
// --- End inlined linearizeDepth ---

// --- Begin inlined viewSpaceDepth ---
float viewSpaceDepth(float depth, float near, float far) {
  float z = depth * (far - near) - far;
  return (far + near + z) / (2.0 * far);
}
// --- End inlined viewSpaceDepth ---

// --- Begin inlined getThickness ---
float getThickness(float insideZ, float outsideZ, float uNear, float uFar) {
  float insideDepthLinear = linearizeDepth(insideZ, uNear, uFar);
  float outsideLinearDepth = linearizeDepth(outsideZ, uNear, uFar);
  // distance from the surface to the inside side
  float depthDifference = insideDepthLinear - outsideLinearDepth;

  return depthDifference;
}
// --- End inlined getThickness ---

// --- Begin inlined packRGB ---
vec3 packRGB(float v) {
  vec3 pack = fract(vec3(1.0, 255.0, 65025.0) * v);
  pack -= pack.yzx * vec3(1.0 / 255.0, 1.0 / 255.0, 0.0);
  return pack;
}
// --- End inlined packRGB ---

// --- Begin inlined unpackRGB ---
float unpackRGB(vec3 pack) {
  return dot(pack, vec3(1.0, 1.0 / 255.0, 1.0 / 65025.0));
}
// --- End inlined unpackRGB ---

out vec4 fragColor;

float getDepth(float raymarchTravel) {
  // Get the current linear depth (distance from camera)
  float viewSpaceZ = linearizeDepth(gl_FragCoord.z, uNear, uFar);

  // Add our raymarch distance - both are now in view space units from camera
  float newViewSpaceZ = viewSpaceZ + raymarchTravel;

  // Convert back to [0,1] depth buffer space
  return uFar * (newViewSpaceZ - uNear) / (newViewSpaceZ * (uFar - uNear));

}

// camera utils

// https://www.geeks3d.com/20141201/how-to-rotate-a-vertex-by-a-quaternion-in-glsl/
vec4 quat_from_axis_angle(vec3 axis, float angle) {
  vec4 qr;
  float half_angle = angle * 0.5 * 3.14159 / 180.0;
  qr.x = axis.x * sin(half_angle);
  qr.y = axis.y * sin(half_angle);
  qr.z = axis.z * sin(half_angle);
  qr.w = cos(half_angle);
  return qr;
}

vec3 rotate_vertex_position(vec3 position, vec3 axis, float angle) {
  vec4 q = quat_from_axis_angle(axis, angle);
  vec3 v = position.xyz;
  return v + 2.0 * cross(q.xyz, cross(q.xyz, v) + q.w * v);
}

vec3 quaterion_rotate(vec3 v, vec4 q) {
  return v + 2.0 * cross(q.xyz, cross(q.xyz, v) + q.w * v);
}

void main() {
  float outsideZ = gl_FragCoord.z;

  vec3 viewDirection = normalize(wPos - cameraPosition);

  RaymarchResult result = rayMarch(wPos, viewDirection, 10.0);
  fragColor = result.color;

  gl_FragDepth = getDepth(result.depth);

  // Vignette/edge darkening effect
  // Calculate distance from center in world space (XZ plane)
  float distFromCenter = length(wPos.xz);
  // Center area radius (3x3 area, so radius ~1.5)
  float vignetteRadius = 1.5;
  float vignetteSoftness = 2.0;
  // Camera zoom/distance effect (use cameraPosition.y as proxy for zoom)
  float zoomFade = smoothstep(2.0, 8.0, cameraPosition.y);
  // Fade based on distance from center, with softness and zoom
  float vignette = 1.0 - smoothstep(vignetteRadius, vignetteRadius + vignetteSoftness + zoomFade * 6.0, distFromCenter);
  // Clamp vignette to [0,1]
  vignette = clamp(vignette, 0.0, 1.0);
  // Fade to black at edges
  fragColor.rgb *= vignette;
}
