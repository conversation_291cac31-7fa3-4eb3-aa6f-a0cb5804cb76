import React, { useMemo, useRef, useEffect, useState } from 'react';
import { useThree, use<PERSON>rame, createPortal } from '@react-three/fiber';
import * as THREE from 'three';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader';
import { useControls } from 'leva';

export default function SceneManager({
  Scene1Component,
  Scene2Component,
  transition = 0,
  globalScrollProgress = 0,
  scene1Progress = 0,
  scene2Progress = 0,
  currentScene = 1,
  isTransitioning = false,
  onScrollUpdate = () => {}
}) {
  const { gl, camera, size } = useThree();

  // Scroll distance controls
  const {
    scrollDistance,
    scrollSensitivity,
    enableCustomScroll
  } = useControls('Scroll Settings', {
    enableCustomScroll: false,
    scrollDistance: { value: 4, min: 2, max: 10, step: 0.5 },
    scrollSensitivity: { value: 0.4, min: 0.1, max: 1.0, step: 0.05 }
  });

  // Create scenes
  const scene1 = useMemo(() => new THREE.Scene(), []);
  const scene2 = useMemo(() => new THREE.Scene(), []);

  // Create render targets
  const renderTarget1 = useMemo(() => new THREE.WebGLRenderTarget(size.width, size.height), [size.width, size.height]);
  const renderTarget2 = useMemo(() => new THREE.WebGLRenderTarget(size.width, size.height), [size.width, size.height]);

  // Load required textures with proper async handling
  const [scrollTexture, setScrollTexture] = useState(null);
  const [frostTexture, setFrostTexture] = useState(null);
  const [blueTexture, setBlueTexture] = useState(null);

  useEffect(() => {
    const loader = new KTX2Loader();
    loader.setTranscoderPath('https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/');
    loader.detectSupport(gl);

    // Load scroll texture
    loader.load('/textures/scroll-datatexture.ktx2', (texture) => {
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      setScrollTexture(texture);
    });

    // Load frost texture
    loader.load('/textures/frost-datatexture.ktx2', (texture) => {
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      setFrostTexture(texture);
    });

    // Load blue texture
    loader.load('/textures/blue-8-128-rgb.ktx2', (texture) => {
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      setBlueTexture(texture);
    });
  }, [gl]);

  // Create advanced transition material with chromatic aberration and displacement effects
  const transitionMaterial = useMemo(() => {
    return new THREE.ShaderMaterial({
      uniforms: {
        tScene1: { value: null },
        tScene2: { value: null },
        tScroll: { value: scrollTexture || null },
        tFrost: { value: frostTexture || null },
        tBlue: { value: blueTexture || null },
        uProgress: { value: 0.0 },
        uProgressVel: { value: 0.0 },
        uBlueOffset: { value: new THREE.Vector2() },
        uCurrentScene: { value: 1.0 },
        // Global uniforms from original code
        resolution: { value: new THREE.Vector2(size.width, size.height) },
        resolutionUI: { value: new THREE.Vector2(size.width, size.height) },
        aspect: { value: size.width / size.height },
        time: { value: 0.0 },
        dtRatio: { value: 1.0 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        // Global uniforms from ae constant
        uniform vec2 resolution;
        uniform vec2 resolutionUI;
        uniform float aspect;
        uniform float time;
        uniform float dtRatio;

        // Utility functions from original code
        float efit(float x, float a1, float a2, float b1, float b2) {
          return b1 + ((x - a1) * (b2 - b1)) / (a2 - a1);
        }

        float fit(float x, float a1, float a2, float b1, float b2) {
          return clamp(efit(x, a1, a2, b1, b2), min(b1, b2), max(b1, b2));
        }

        vec3 efit(vec3 x, vec3 a1, vec3 a2, vec3 b1, vec3 b2) {
          return b1 + ((x - a1) * (b2 - b1)) / (a2 - a1);
        }

        vec3 fit(vec3 x, vec3 a1, vec3 a2, vec3 b1, vec3 b2) {
          return clamp(efit(x, a1, a2, b1, b2), min(b1, b2), max(b1, b2));
        }

        float fit01(float x, float a1, float a2) {
          return fit(x, 0.0, 1.0, a1, a2);
        }

        float fit10(float x, float a1, float a2) {
          return fit(x, 1.0, 0.0, a1, a2);
        }

        float fit11(float x, float a1, float a2) {
          return fit(x, -1.0, 1.0, a1, a2);
        }

        vec3 fit01(vec3 x, vec3 a1, vec3 a2) {
          return fit(x, vec3(0.0), vec3(1.0), a1, a2);
        }

        vec3 fit10(vec3 x, vec3 a1, vec3 a2) {
          return fit(x, vec3(1.0), vec3(0.0), a1, a2);
        }

        vec3 fit11(vec3 x, vec3 a1, vec3 a2) {
          return fit(x, vec3(-1.0), vec3(1.0), a1, a2);
        }

        // Anti-aliased step function
        float aastep(float threshold, float value) {
          float afwidth = 0.7 * length(vec2(dFdx(value), dFdy(value)));
          return smoothstep(threshold - afwidth, threshold + afwidth, value);
        }

        // CORRECT falloff functions from Ue constant - this was the missing piece!
        float _linstep(float begin, float end, float t) {
          return clamp((t - begin) / (end - begin), 0.0, 1.0);
        }

        float falloff(float _input, float start, float end, float margin, float progress) {
          float m = margin * sign(end - start);
          float p = mix(start - m, end, progress);
          return _linstep(p + m, p, _input);
        }

        // Chromatic aberration functions
        float ca_linterp(float t) {
          return clamp(1.0 - abs(2.0 * t - 1.0), 0.0, 1.0);
        }

        float ca_remap(float t, float a, float b) {
          return clamp((t - a) / (b - a), 0.0, 1.0);
        }

        vec2 ca_barrelDistortion(vec2 coord, float amt) {
          vec2 cc = coord - 0.5;
          float dist = dot(cc, cc);
          return coord + cc * dist * amt;
        }

        vec4 ca_spectrum_offset(float t) {
          vec4 ret;
          float lo = step(t, 0.5);
          float hi = 1.0 - lo;
          float w = ca_linterp(ca_remap(t, 1.0/6.0, 5.0/6.0));
          ret = vec4(lo, 1.0, hi, 1.0) * vec4(1.0 - w, w, 1.0 - w, 1.0);
          return pow(ret, vec4(1.0/2.2));
        }

        const int CA_ITERATIONS = 5;
        const float RECI_ITER = 1.0 / float(CA_ITERATIONS);

        vec4 chromatic_aberration(sampler2D text, vec2 uv, float maxdistort, float bendAmount) {
          vec4 sumcol = vec4(0.0);
          vec4 sumw = vec4(0.0);
          for(int i = 0; i < CA_ITERATIONS; ++i) {
            float t = float(i) * RECI_ITER;
            vec4 w = ca_spectrum_offset(t);
            sumw += w;
            sumcol += w * texture2D(text, ca_barrelDistortion(uv, bendAmount * maxdistort * t));
          }
          return sumcol / sumw;
        }

        // Easing functions
        float power2In(float t) {
          return t * t * t;
        }

        float power2Out(float t) {
          float f = t - 1.0;
          return f * f * f + 1.0;
        }

        // Noise function
        vec4 getNoise(sampler2D tex, vec2 uv, vec2 offset) {
          float invSize = 1.0 / float(textureSize(tex, 0).x);
          return texture(tex, uv * invSize + offset);
        }

        uniform sampler2D tScene1;
        uniform sampler2D tScene2;
        uniform sampler2D tScroll;
        uniform sampler2D tFrost;
        uniform sampler2D tBlue;
        uniform float uProgress;
        uniform float uProgressVel;
        uniform vec2 uBlueOffset;
        uniform float uCurrentScene; // 1.0 for Scene1, 2.0 for Scene2

        varying vec2 vUv;

        void main() {
          vec3 color = vec3(0.0);

          if (uProgress >= 1.0) {
            // When transition is complete, show Scene2 directly
            color = texture2D(tScene2, vUv).rgb;
          } else if (uProgress > 0.0) {
            // Cut effect - EXACT from original code
            vec2 uvTex = vUv - 0.5;
            uvTex.x *= aspect;
            uvTex += 0.5;

            // Safe texture sampling with fallback
            vec3 scrollTex = vec3(0.5);
            if (textureSize(tScroll, 0).x > 1) {
              scrollTex = texture2D(tScroll, uvTex * 1.0).rgb;
            }

            // Slope displacement - EXACT from original
            float slopeDisp = (scrollTex.b * 2.0 - 1.0) * 0.4;
            float slope = -0.2 * aspect * step(0.0, uProgress);
            float inclination = mix(1.0 - vUv.x + slopeDisp, vUv.x + slopeDisp, step(slope, 0.0));
            float incProgress = fit(uProgress, 0.0, 1.0, 0.0, 1.0 + abs(slope));

            // Chromatic aberration - EXACT from original
            float cutDiagonalBlur = falloff(vUv.y + inclination * abs(slope), 0.0, 1.0, 2.0, incProgress);

            // Tech displacement - EXACT from original
            float cutDiagonalDisplacement = falloff(vUv.y + inclination * abs(slope), 0.0, 1.0, 0.9, incProgress);
            float cutDisp = falloff(scrollTex.g, 0.0, 1.0, 1.0, cutDiagonalDisplacement);

            // Ice cut - EXACT from original
            float cutDiagonal = falloff(vUv.y + inclination * abs(slope), 0.0, 1.0, 0.2, incProgress);
            float cut = falloff(scrollTex.r, 0.0, 1.0, 2.0, cutDiagonal);

            // Variables - EXACT from original
            const float parallaxY = 0.4;
            const float displacement = 0.025;

            // Add blue noise to hide the seams from the chromatic aberration - EXACT from original
            vec4 noise = vec4(0.5);
            if (textureSize(tBlue, 0).x > 1) {
              noise = getNoise(tBlue, gl_FragCoord.xy, uBlueOffset);
            }

            // Mix with optimization to save texture reads - EXACT from original
            vec3 scene1 = vec3(0.0);
            vec3 scene2 = vec3(0.0);
            float modulator = 12.0 * smoothstep(1.0, 0.7, abs(vUv.x * 2.0 - 1.0)) * smoothstep(1.0, 0.7, abs(vUv.y * 2.0 - 1.0));

            if (cut < 1.0) {
              scene1 = chromatic_aberration(tScene1, vUv - vec2(0.0, parallaxY * power2In(uProgress) + displacement * cutDisp), modulator, cutDiagonalBlur * noise.r).rgb;
            }
            if (cut > 0.0) {
              scene2 = chromatic_aberration(tScene2, vUv + vec2(0.0, parallaxY * power2In(1.0 - uProgress) + displacement * (1.0 - cutDisp)), modulator, (1.0 - cutDiagonalBlur) * noise.g).rgb;
            }

            color = clamp(mix(scene1, scene2, cut), vec3(0.0), vec3(1.0));
          } else {
            // When no transition, show the appropriate scene
            if (uCurrentScene >= 2.0) {
              color = texture2D(tScene2, vUv).rgb;
            } else {
              color = texture2D(tScene1, vUv).rgb;
            }
          }

          gl_FragColor = vec4(color, 1.0);
        }
      `
    });
  }, [scrollTexture, frostTexture, blueTexture, size]);

  // Create post-processing scene
  const postScene = useMemo(() => {
    const scene = new THREE.Scene();
    const quad = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      transitionMaterial
    );
    scene.add(quad);
    return scene;
  }, [transitionMaterial]);

  const postCamera = useMemo(() => new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1), []);

  // Track previous transition value for velocity calculation
  const prevTransition = useRef(transition);
  const transitionVelocity = useRef(0);

  // Global scroll state management
  const virtualScrollRef = useRef(0);
  const targetScrollRef = useRef(0);
  const scrollVelocityRef = useRef(0);

  // Update transition progress and calculate velocity
  useEffect(() => {
    transitionMaterial.uniforms.uProgress.value = transition;
    transitionMaterial.uniforms.uCurrentScene.value = currentScene;

    // Calculate velocity (change in transition over time)
    const deltaTransition = transition - prevTransition.current;
    transitionVelocity.current = deltaTransition;
    transitionMaterial.uniforms.uProgressVel.value = Math.abs(deltaTransition) * 10; // Scale velocity

    prevTransition.current = transition;
  }, [transition, currentScene, transitionMaterial]);

  // Update global uniforms when size changes
  useEffect(() => {
    transitionMaterial.uniforms.resolution.value.set(size.width, size.height);
    transitionMaterial.uniforms.resolutionUI.value.set(size.width, size.height);
    transitionMaterial.uniforms.aspect.value = size.width / size.height;
  }, [size, transitionMaterial]);

  // Update material uniforms when textures are loaded
  useEffect(() => {
    if (scrollTexture) {
      transitionMaterial.uniforms.tScroll.value = scrollTexture;
    }
  }, [scrollTexture, transitionMaterial]);

  useEffect(() => {
    if (frostTexture) {
      transitionMaterial.uniforms.tFrost.value = frostTexture;
    }
  }, [frostTexture, transitionMaterial]);

  useEffect(() => {
    if (blueTexture) {
      transitionMaterial.uniforms.tBlue.value = blueTexture;
    }
  }, [blueTexture, transitionMaterial]);

  // Global scroll event handling - captures all scroll events
  useEffect(() => {
    const handleWheel = (event) => {
      // Prevent default scroll behavior
      event.preventDefault();
      event.stopPropagation();

      // Update target scroll position with improved sensitivity
      const deltaY = event.deltaY;
      const currentSensitivity = enableCustomScroll ? scrollSensitivity : 0.4;
      const currentMaxScroll = window.innerHeight * (enableCustomScroll ? scrollDistance : 4);

      // Update target scroll position
      targetScrollRef.current = Math.max(0, Math.min(currentMaxScroll, targetScrollRef.current + deltaY * currentSensitivity));

      // Add velocity for momentum
      scrollVelocityRef.current = deltaY * currentSensitivity * 0.05; // Reduced momentum
    };

    // Handle keyboard scrolling (arrow keys, page up/down, space)
    const handleKeyDown = (event) => {
      let scrollAmount = 0;

      switch(event.code) {
        case 'ArrowDown':
        case 'Space':
          scrollAmount = window.innerHeight * 0.15; // Adjusted for shorter total distance
          break;
        case 'ArrowUp':
          scrollAmount = -window.innerHeight * 0.15;
          break;
        case 'PageDown':
          scrollAmount = window.innerHeight * 0.3; // Adjusted for shorter total distance
          break;
        case 'PageUp':
          scrollAmount = -window.innerHeight * 0.3;
          break;
        default:
          return; // Don't prevent default for other keys
      }

      event.preventDefault();
      event.stopPropagation();

      const keyboardMaxScroll = window.innerHeight * (enableCustomScroll ? scrollDistance : 4);
      targetScrollRef.current = Math.max(0, Math.min(keyboardMaxScroll, targetScrollRef.current + scrollAmount));
    };

    // Add event listeners
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [enableCustomScroll, scrollDistance, scrollSensitivity]);

  // Render loop
  useFrame(({ clock, camera }, delta) => {
    // Smooth interpolation towards target scroll position
    const lerpFactor = 1 - Math.pow(0.001, delta); // Smooth exponential interpolation
    virtualScrollRef.current = THREE.MathUtils.lerp(virtualScrollRef.current, targetScrollRef.current, lerpFactor);

    // Apply velocity decay
    scrollVelocityRef.current *= 0.95;

    // Calculate global scroll progress (0 to 1)
    const renderMaxScroll = window.innerHeight * (enableCustomScroll ? scrollDistance : 4);
    const globalProgress = Math.max(0, Math.min(1, virtualScrollRef.current / renderMaxScroll));

    // Update parent component with scroll progress
    onScrollUpdate(globalProgress);

    // Let the camera system handle positioning naturally

    // Update global time uniform
    transitionMaterial.uniforms.time.value = clock.elapsedTime;

    // Update blue noise offset for animation
    transitionMaterial.uniforms.uBlueOffset.value.set(
      Math.random() * 10,
      Math.random() * 12.5
    );

    // Render Scene1 to renderTarget1
    gl.setRenderTarget(renderTarget1);
    gl.render(scene1, camera);

    // Always render Scene2 when it's the current scene or during transition
    const shouldRenderScene2 = currentScene === 2 || isTransitioning || transition > 0.01;
    if (shouldRenderScene2) {
      gl.setRenderTarget(renderTarget2);
      gl.render(scene2, camera);
      transitionMaterial.uniforms.tScene2.value = renderTarget2.texture;
    } else {
      // When Scene2 shouldn't be rendered, use Scene1's texture as fallback
      transitionMaterial.uniforms.tScene2.value = renderTarget1.texture;
    }

    // Set Scene1 texture
    transitionMaterial.uniforms.tScene1.value = renderTarget1.texture;

    // Render final transition
    gl.setRenderTarget(null);
    gl.render(postScene, postCamera);
  }, 1);

  // Determine which scene is active based on current scene and transition state
  // Scene1 is active during Scene1 phase only
  // Scene2 is active during Scene2 phase only
  // During transition, neither scene is active (transition takes over)
  const scene1Active = currentScene === 1 && !isTransitioning;
  const scene2Active = currentScene === 2 && !isTransitioning;

  return (
    <>
      {/* Render Scene1 and Scene2 to their respective scenes with active scene info */}
      {createPortal(
        <Scene1Component
          isActive={scene1Active}
          scrollProgress={scene1Progress}
          globalScrollProgress={globalScrollProgress}
        />,
        scene1
      )}
      {createPortal(
        <Scene2Component
          isActive={scene2Active}
          scrollProgress={scene2Progress}
          globalScrollProgress={globalScrollProgress}
        />,
        scene2
      )}
    </>
  );
}
