import { Suspense, useState, useRef, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { useControls } from 'leva';
import './AppAI.css';

// Import modular components
import SceneManager from './components/SceneManager/SceneManager';
import Scene1 from './components/SceneManager/Scene1';
import Scene2 from './components/SceneManager/Scene2';
import CameraController from './components/SceneManager/CameraController';
import Scene1Overlay from './components/SceneManager/Scene1Overlay';
import PerformanceAnalyzer from './components/Performance/PerformanceAnalyzer';
import OptimizationGuide from './components/Performance/OptimizationGuide';

// Main AppAI Component with scroll-based transitions
export default function AppAI() {
  // Global scroll state management
  const [globalScrollProgress, setGlobalScrollProgress] = useState(0); // 0 to 1 across all scenes
  const [currentScene, setCurrentScene] = useState(1); // 1 or 2
  const [transitionProgress, setTransitionProgress] = useState(0); // 0 to 1 during transition
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Virtual scroll state
  const virtualScrollRef = useRef(0);
  const targetScrollRef = useRef(0);
  const scrollVelocityRef = useRef(0);

  // Leva controls for manual override (for debugging)
  const {
    manualTransition,
    enableManualControl,
    showDebugInfo
  } = useControls('Scene Transition', {
    manualTransition: { value: 0, min: 0, max: 1, step: 0.01 },
    enableManualControl: false,
    showDebugInfo: true
  });

  // Debug info display
  useEffect(() => {
    if (showDebugInfo) {
      console.log('AppAI Debug:', {
        globalScrollProgress: globalScrollProgress.toFixed(3),
        currentScene,
        transitionProgress: transitionProgress.toFixed(3),
        isTransitioning,
        virtualScroll: virtualScrollRef.current.toFixed(1),
        targetScroll: targetScrollRef.current.toFixed(1)
      });
    }
  }, [globalScrollProgress, currentScene, transitionProgress, isTransitioning, showDebugInfo]);

  // Calculate scroll phases based on global progress
  // Phase 1: Scene1 (0% to 40%)
  // Phase 2: Transition (40% to 60%)
  // Phase 3: Scene2 (60% to 100%)
  const scene1Phase = globalScrollProgress <= 0.4;
  const transitionPhase = globalScrollProgress > 0.4 && globalScrollProgress <= 0.6;
  const scene2Phase = globalScrollProgress > 0.6;

  // Calculate individual progress values
  const scene1Progress = scene1Phase ? Math.min(1, globalScrollProgress / 0.4) : 1;
  const calculatedTransitionProgress = transitionPhase ? (globalScrollProgress - 0.4) / 0.2 : 0;
  const scene2Progress = scene2Phase ? (globalScrollProgress - 0.6) / 0.4 : 0;

  // Update scene states based on scroll progress
  useEffect(() => {
    if (scene1Phase) {
      setCurrentScene(1);
      setIsTransitioning(false);
      setTransitionProgress(0);
    } else if (transitionPhase) {
      setCurrentScene(1); // Still Scene1 during transition
      setIsTransitioning(true);
      setTransitionProgress(calculatedTransitionProgress);
    } else if (scene2Phase) {
      setCurrentScene(2);
      setIsTransitioning(false);
      setTransitionProgress(0); // No transition when in Scene2
    }
  }, [scene1Phase, transitionPhase, scene2Phase, calculatedTransitionProgress]);

  return (
    <div style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      {/* Fixed 3D background - now fullscreen without scroll */}
      <Canvas
        camera={{ position: [0, 0.35, 2], fov: 45 }}
        gl={{ alpha: true, antialias: true, powerPreference: 'high-performance' }}
        style={{ position: 'absolute', top: 0, left: 0, width: '100vw', height: '100vh', zIndex: 0 }}
      >
        <Suspense fallback={null}>
          <CameraController />
          <SceneManager
            Scene1Component={Scene1}
            Scene2Component={Scene2}
            transition={enableManualControl ? manualTransition : transitionProgress}
            globalScrollProgress={globalScrollProgress}
            scene1Progress={scene1Progress}
            scene2Progress={scene2Progress}
            currentScene={currentScene}
            isTransitioning={isTransitioning}
            onScrollUpdate={setGlobalScrollProgress}
          />
        </Suspense>
      </Canvas>

      {/* Scene1 Overlay - HTML/CSS Content */}
      <Scene1Overlay
        scrollProgress={scene1Progress}
        isVisible={currentScene === 1 && !isTransitioning}
      />

      {/* Performance Analyzer */}
      <PerformanceAnalyzer />

      {/* Performance Optimization Guide */}
      <OptimizationGuide />

      {/* Debug overlay */}
      {showDebugInfo && (
        <div style={{
          position: 'absolute',
          top: '50px',
          right: '10px',
          background: 'rgba(0,0,0,0.8)',
          color: 'red',
          padding: '10px',
          borderRadius: '5px',
          fontFamily: 'monospace',
          fontSize: '12px',
          zIndex: 1000
        }}>
          <div>Global Scroll: {(globalScrollProgress * 100).toFixed(1)}%</div>
          <div>Current Scene: {currentScene}</div>
          <div>Transition: {(transitionProgress * 100).toFixed(1)}%</div>
          <div>Is Transitioning: {isTransitioning ? 'Yes' : 'No'}</div>
          <div>Scene1 Progress: {(scene1Progress * 100).toFixed(1)}%</div>
          <div>Scene2 Progress: {(scene2Progress * 100).toFixed(1)}%</div>
        </div>
      )}
    </div>
  );
}
